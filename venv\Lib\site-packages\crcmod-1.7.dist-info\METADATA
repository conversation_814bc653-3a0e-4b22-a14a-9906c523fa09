Metadata-Version: 2.4
Name: crcmod
Version: 1.7
Summary: CRC Generator
Home-page: http://crcmod.sourceforge.net/
Download-URL: http://sourceforge.net/projects/crcmod
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: C
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.4
Classifier: Programming Language :: Python :: 2.5
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.1
Classifier: Topic :: Communications
Classifier: Topic :: Scientific/Engineering :: Interface Engine/Protocol Translator
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Utilities
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: download-url
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: summary

===========================
crcmod for Calculating CRCs
===========================

The software in this package is a Python module for generating objects that
compute the Cyclic Redundancy Check (CRC).  There is no attempt in this package
to explain how the CRC works.  There are a number of resources on the web that
give a good explanation of the algorithms.  Just do a Google search for "crc
calculation" and browse till you find what you need.  Another resource can be
found in chapter 20 of the book "Numerical Recipes in C" by Press et. al.

This package allows the use of any 8, 16, 24, 32, or 64 bit CRC.  You can
generate a Python function for the selected polynomial or an instance of the
Crc class which provides the same interface as the ``md5`` and ``sha`` modules
from the Python standard library.  A ``Crc`` class instance can also generate
C/C++ source code that can be used in another application.

----------
Guidelines
----------

Documentation is available from the doc strings.  It is up to you to decide
what polynomials to use in your application.  If someone has not specified the
polynomials to use, you will need to do some research to find one suitable for
your application.  Examples are available in the unit test script ``test.py``.
You may also use the ``predefined`` module to select one of the standard
polynomials.

If you need to generate code for another language, I suggest you subclass the
``Crc`` class and replace the method ``generateCode``.  Use ``generateCode`` as
a model for the new version.

------------
Dependencies
------------

Python Version
^^^^^^^^^^^^^^

The package has separate code to support the 2.x and 3.x Python series.

For the 2.x versions of Python, these versions have been tested:

* 2.4
* 2.5
* 2.6
* 2.7

It may still work on earlier versions of Python 2.x, but these have not been
recently tested.

For the 3.x versions of Python, these versions have been tested:

* 3.1

Building C extension
^^^^^^^^^^^^^^^^^^^^

To build the C extension, the appropriate compiler tools for your platform must
be installed. Refer to the Python documentation for building C extensions for
details.

------------
Installation
------------

The crcmod package is installed using ``distutils``.
Run the following command::

    python setup.py install

If the extension module builds, it will be installed.  Otherwise, the
installation will include the pure Python version.  This will run significantly
slower than the extension module but will allow the package to be used.

For Windows users who want to use the mingw32 compiler, run this command::

    python setup.py build --compiler=mingw32 install

For Python 3.x, the install process is the same but you need to use the 3.x
interpreter.

------------
Unit Testing
------------

The ``crcmod`` package has a module ``crcmod.test``, which contains unit
tests for both ``crcmod`` and ``crcmod.predefined``.

When you first install ``crcmod``, you should run the unit tests to make sure
everything is installed properly.  The test script performs a number of tests
including a comparison to the direct method which uses a class implementing
polynomials over the integers mod 2.

To run the unit tests on Python >=2.5::

    python -m crcmod.test

Alternatively, in the ``test`` directory run::

    python test_crcmod.py

---------------
Code Generation
---------------

The crcmod package is capable of generating C functions that can be compiled
with a C or C++ compiler.  In the test directory, there is an examples.py
script that demonstrates how to use the code generator.  The result of this is
written out to the file ``examples.c``.  The generated code was checked to make
sure it compiles with the GCC compiler.

-------
License
-------

The ``crcmod`` package is released under the MIT license. See the ``LICENSE``
file for details.

------------
Contributors
------------

Craig McQueen
