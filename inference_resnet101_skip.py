#!/usr/bin/env python
# inference_resnet101_skip.py - Inferenza ResNet-101 skip su tutto il dataset DAVIS 16/17
# Organizza i risultati nella struttura: inferenze_davis_16_17/{split}/{video_name}_inference_101_skip/

import argparse
import re
from collections import defaultdict
from pathlib import Path

import torch
from PIL import Image
from torch.utils.data import DataLoader
from tqdm import tqdm

from binary_mask_dataset import BinaryMaskSequenceDataset
from unet_smp import build_model


# ---------- CLI ---------------------------------------------------------------- #
def cli():
    p = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Inferenza ResNet-101 skip su tutto il dataset DAVIS 16/17."
    )
    p.add_argument("--data", default="dataset_davis_2016_2017_skip", 
                   help="Root dataset skip (train/val/test)")
    p.add_argument("--model", default="resnet101_skip/best_model.pth", 
                   help="Checkpoint .pth del modello ResNet-101 skip")
    p.add_argument("--out", default="inferenze_davis_16_17", 
                   help="Cartella di output")
    p.add_argument("--davis-root", default="DAVIS", 
                   help="Cartella DAVIS originale per mappare i nomi")
    p.add_argument("--splits", nargs="+", default=["train", "val", "test"],
                   help="Split da processare")
    p.add_argument("--batch", type=int, default=4, help="Batch-size inferenza")
    p.add_argument("--thr", type=float, default=0.5, help="Soglia binarizzazione")
    return p.parse_args()


# ---------- helper ------------------------------------------------------------- #
def ensure(p: Path): 
    p.mkdir(parents=True, exist_ok=True)


def save_mask(arr, path: Path):
    Image.fromarray(arr.astype("uint8")).save(path)


def create_video_name_mapping(davis_root: Path):
    """
    Crea una mappatura tra i numeri usati nel dataset e i nomi originali dei video DAVIS.
    Analizza i file nel dataset per capire la corrispondenza.
    """
    # Mappa i nomi dei video DAVIS originali
    davis_videos = []
    jpeg_dir = davis_root / "JPEGImages" / "480p"
    if jpeg_dir.exists():
        davis_videos = sorted([d.name for d in jpeg_dir.iterdir() if d.is_dir()])
    
    print(f"📁 Trovati {len(davis_videos)} video DAVIS originali")
    
    # Crea mappatura numerica -> nome video
    # I numeri nel dataset corrispondono all'ordine alfabetico dei video DAVIS
    video_mapping = {}
    for i, video_name in enumerate(davis_videos, 1):
        video_mapping[str(i)] = video_name
    
    return video_mapping


def extract_video_info(sample_name: str):
    """
    Estrae informazioni dal nome del sample.
    Es: "1_00037_s2" -> video_id="1", frame="00037", step="s2"
    Es: "train_4_00020" -> video_id="train_4", frame="00020", step=None
    """
    # Pattern per diversi formati
    patterns = [
        r"^(train_\d+)_(\d+)(?:_s(\d+))?$",  # train_4_00020 o train_4_00020_s2
        r"^(\d+)_(\d+)(?:_s(\d+))?$",        # 1_00037 o 1_00037_s2
    ]
    
    for pattern in patterns:
        match = re.match(pattern, sample_name)
        if match:
            video_id = match.group(1)
            frame = match.group(2)
            step = match.group(3) if match.group(3) else None
            return video_id, frame, step
    
    # Fallback: prova a dividere per underscore
    parts = sample_name.split("_")
    if len(parts) >= 2:
        video_id = parts[0]
        frame = parts[1]
        step = parts[2] if len(parts) > 2 and parts[2].startswith('s') else None
        return video_id, frame, step
    
    return sample_name, "00000", None


# ---------- main logic --------------------------------------------------------- #
@torch.no_grad()
def run_split(split: str, root: Path, model, out_root: Path, batch: int, thr: float, 
              device, video_mapping: dict):
    """
    Esegue l'inferenza su uno split e organizza i risultati per video.
    """
    ds = BinaryMaskSequenceDataset(root / split / "X", root / split / "Y", shuffle=False)
    ld = DataLoader(ds, batch_size=batch, pin_memory=torch.cuda.is_available())
    
    # Raggruppa i samples per video
    video_samples = defaultdict(list)
    for sample in ds.samples:
        video_id, frame, step = extract_video_info(sample)
        video_samples[video_id].append((sample, frame, step))
    
    print(f"📊 Split '{split}': {len(ds)} samples, {len(video_samples)} video")
    
    # Processa tutti i samples
    for idx, (x, y) in enumerate(tqdm(ld, desc=f"[{split}] Inferenza"), 1):
        x, y = x.to(device), y.to(device)
        logits = model(x)
        pred = (torch.sigmoid(logits) > thr).float().cpu().numpy()
        
        for b in range(x.size(0)):
            sample_idx = (idx - 1) * batch + b
            if sample_idx >= len(ds.samples):
                break
                
            sample_name = ds.samples[sample_idx]
            video_id, frame, step = extract_video_info(sample_name)
            
            # Determina il nome del video di output
            if video_id in video_mapping:
                video_name = video_mapping[video_id]
            else:
                # Fallback per video non mappati
                video_name = f"video_{video_id}"
            
            # Crea la cartella di output per questo video
            video_dir = out_root / split / f"{video_name}_inference_101_skip"
            ensure(video_dir)
            
            # Salva la predizione
            frame_name = f"{frame}.png"
            if step:
                frame_name = f"{frame}_s{step}.png"
            
            save_mask(pred[b, 0] * 255, video_dir / frame_name)
    
    print(f"✔ Split '{split}' completato – {len(ds)} frame processati")


def main():
    args = cli()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🚀 Usando device: {device}")
    
    # Carica il modello
    print("📦 Caricamento modello ResNet-101 skip...")
    model = build_model().to(device)
    model.load_state_dict(torch.load(args.model, map_location=device))
    model.eval()
    print("✅ Modello caricato")
    
    # Crea mappatura video
    print("🗺️  Creazione mappatura nomi video...")
    davis_root = Path(args.davis_root)
    video_mapping = create_video_name_mapping(davis_root)
    print(f"✅ Mappatura creata: {len(video_mapping)} video")
    
    # Prepara cartelle
    root = Path(args.data).resolve()
    out_root = Path(args.out).resolve()
    ensure(out_root)
    
    print(f"📂 Dataset: {root}")
    print(f"📁 Output: {out_root}")
    
    # Processa tutti gli split
    for split in args.splits:
        if not (root / split).exists():
            print(f"⚠️  Split '{split}' non trovato, salto...")
            continue
        run_split(split, root, model, out_root, args.batch, args.thr, device, video_mapping)
    
    print(f"\n🎉 Inferenze ResNet-101 skip completate!")
    print(f"📁 Risultati salvati in: {out_root}")


if __name__ == "__main__":
    main()
