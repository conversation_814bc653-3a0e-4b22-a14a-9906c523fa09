#!/usr/bin/env python
# inference_resnet101_skip.py - Inferenza ResNet-101 skip su tutto il dataset DAVIS 16/17
# Organizza i risultati nella struttura: inferenze_davis_16_17/{split}/{video_name}_inference_101_skip/

import argparse
import re
from collections import defaultdict
from pathlib import Path

import torch
from PIL import Image
from torch.utils.data import DataLoader
from tqdm import tqdm

from binary_mask_dataset import BinaryMaskSequenceDataset
from unet_smp import build_model


# ---------- CLI ---------------------------------------------------------------- #
def cli():
    p = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Inferenza ResNet-101 skip su tutto il dataset DAVIS 16/17."
    )
    p.add_argument("--data", default="dataset_davis_2016_2017_skip", 
                   help="Root dataset skip (train/val/test)")
    p.add_argument("--model", default="resnet101_skip/best_model.pth", 
                   help="Checkpoint .pth del modello ResNet-101 skip")
    p.add_argument("--out", default="inferenze_davis_16_17", 
                   help="Cartella di output")
    p.add_argument("--davis-root", default="DAVIS", 
                   help="Cartella DAVIS originale per mappare i nomi")
    p.add_argument("--splits", nargs="+", default=["train", "val", "test"],
                   help="Split da processare")
    p.add_argument("--batch", type=int, default=4, help="Batch-size inferenza")
    p.add_argument("--thr", type=float, default=0.5, help="Soglia binarizzazione")
    return p.parse_args()


# ---------- helper ------------------------------------------------------------- #
def ensure(p: Path): 
    p.mkdir(parents=True, exist_ok=True)


def save_mask(arr, path: Path):
    Image.fromarray(arr.astype("uint8")).save(path)


def create_video_name_mapping(davis_root: Path):
    """
    Crea una mappatura tra i numeri usati nel dataset e i nomi originali dei video DAVIS.
    Analizza i file nel dataset per capire la corrispondenza.
    """
    # Mappa i nomi dei video DAVIS originali
    davis_videos = []
    jpeg_dir = davis_root / "JPEGImages" / "480p"
    if jpeg_dir.exists():
        davis_videos = sorted([d.name for d in jpeg_dir.iterdir() if d.is_dir()])
    
    print(f"📁 Trovati {len(davis_videos)} video DAVIS originali")
    
    # Crea mappatura numerica -> nome video
    # I numeri nel dataset corrispondono all'ordine alfabetico dei video DAVIS
    video_mapping = {}
    for i, video_name in enumerate(davis_videos, 1):
        video_mapping[str(i)] = video_name
    
    return video_mapping


def extract_video_info(sample_name: str):
    """
    Estrae informazioni dal nome del sample skip.
    Es: "bike-packing_1_00004_t0" -> video_name="bike-packing", video_id="1", frame="00004", context="t0"
    Es: "bear_00037_t1" -> video_name="bear", video_id=None, frame="00037", context="t1"
    """
    # Rimuove l'estensione se presente
    base_name = sample_name.replace('.png', '')

    # Pattern per formato: nomeVideo_numeroVideo_numeroFrame_tContesto
    # Es: bike-packing_1_00004_t0
    pattern1 = r"^(.+)_(\d+)_(\d+)_t(\d+)$"
    match = re.match(pattern1, base_name)
    if match:
        video_name = match.group(1)
        video_id = match.group(2)
        frame = match.group(3)
        context = match.group(4)
        return video_name, video_id, frame, context

    # Pattern per formato: nomeVideo_numeroFrame_tContesto
    # Es: bear_00037_t1
    pattern2 = r"^(.+)_(\d+)_t(\d+)$"
    match = re.match(pattern2, base_name)
    if match:
        video_name = match.group(1)
        video_id = None
        frame = match.group(2)
        context = match.group(3)
        return video_name, video_id, frame, context

    # Fallback
    return base_name, None, "00000", "0"


def get_base_name(sample_name: str):
    """
    Ottiene il nome base senza il suffisso _tX.
    Es: "bike-packing_1_00004_t0" -> "bike-packing_1_00004"
    """
    base_name = sample_name.replace('.png', '')
    # Rimuove _tX dalla fine
    if '_t' in base_name:
        return base_name.rsplit('_t', 1)[0]
    return base_name


# ---------- main logic --------------------------------------------------------- #
@torch.no_grad()
def run_split(split: str, root: Path, model, out_root: Path, batch: int, thr: float,
              device):
    """
    Esegue l'inferenza su uno split e organizza i risultati per video.
    """
    ds = BinaryMaskSequenceDataset(root / split / "X", root / split / "Y", shuffle=False)
    ld = DataLoader(ds, batch_size=batch, pin_memory=torch.cuda.is_available())

    # Raggruppa i samples per nome base (senza _tX)
    base_samples = defaultdict(list)
    for sample in ds.samples:
        base_name = get_base_name(sample)
        base_samples[base_name].append(sample)

    # Raggruppa per video
    video_samples = defaultdict(list)
    for base_name in base_samples:
        video_name, _, _, _ = extract_video_info(base_name + "_t0")  # Usa t0 per estrarre info
        video_samples[video_name].append(base_name)

    print(f"📊 Split '{split}': {len(ds)} samples totali, {len(base_samples)} frame target, {len(video_samples)} video")

    # Processa tutti i samples
    for idx, (x, y) in enumerate(tqdm(ld, desc=f"[{split}] Inferenza"), 1):
        x, y = x.to(device), y.to(device)
        logits = model(x)
        pred = (torch.sigmoid(logits) > thr).float().cpu().numpy()

        for b in range(x.size(0)):
            sample_idx = (idx - 1) * batch + b
            if sample_idx >= len(ds.samples):
                break

            sample_name = ds.samples[sample_idx]
            base_name = get_base_name(sample_name)
            video_name, _, _, context = extract_video_info(sample_name)

            # Solo processa i frame t0 (primo del gruppo) per evitare duplicati
            if context != "0":
                continue

            # Crea la cartella di output per questo video
            video_dir = out_root / split / f"{video_name}_inference_101_skip"
            ensure(video_dir)

            # Salva la predizione con il nome base
            frame_name = f"{base_name}.png"
            save_mask(pred[b, 0] * 255, video_dir / frame_name)

    print(f"✔ Split '{split}' completato – {len(ds)} frame processati")


def main():
    args = cli()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🚀 Usando device: {device}")
    
    # Carica il modello
    print("📦 Caricamento modello ResNet-101 skip...")
    model = build_model().to(device)
    model.load_state_dict(torch.load(args.model, map_location=device))
    model.eval()
    print("✅ Modello caricato")
    
    # Crea mappatura video
    print("🗺️  Creazione mappatura nomi video...")
    davis_root = Path(args.davis_root)
    video_mapping = create_video_name_mapping(davis_root)
    print(f"✅ Mappatura creata: {len(video_mapping)} video")
    
    # Prepara cartelle
    root = Path(args.data).resolve()
    out_root = Path(args.out).resolve()
    ensure(out_root)
    
    print(f"📂 Dataset: {root}")
    print(f"📁 Output: {out_root}")
    
    # Processa tutti gli split
    for split in args.splits:
        if not (root / split).exists():
            print(f"⚠️  Split '{split}' non trovato, salto...")
            continue
        run_split(split, root, model, out_root, args.batch, args.thr, device)
    
    print(f"\n🎉 Inferenze ResNet-101 skip completate!")
    print(f"📁 Risultati salvati in: {out_root}")


if __name__ == "__main__":
    main()
