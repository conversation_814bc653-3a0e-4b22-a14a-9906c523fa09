#!/usr/bin/env python
# reorganize_inference_structure.py - Riorganizza la struttura delle inferenze
# Da: video_frame_inference_101_skip/ -> frame.png
# A:  video_inference_101_skip/ -> tutti i frame del video

import argparse
import shutil
from collections import defaultdict
from pathlib import Path
from tqdm import tqdm


def cli():
    p = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Riorganizza la struttura delle inferenze raggruppando i frame per video."
    )
    p.add_argument("--input", default="inferenze_davis_16_17", 
                   help="Cartella di input con la struttura attuale")
    p.add_argument("--output", default="inferenze_davis_16_17_reorganized", 
                   help="Cartella di output con la nuova struttura")
    p.add_argument("--splits", nargs="+", default=["train", "val", "test"],
                   help="Split da processare")
    return p.parse_args()


def ensure(p: Path): 
    p.mkdir(parents=True, exist_ok=True)


def extract_video_info(folder_name: str):
    """
    Estrae informazioni dal nome della cartella.
    Es: "bike-packing_1_00004_inference_101_skip" -> video="bike-packing_1", frame="00004"
    Es: "1_00003_inference_101_skip" -> video="1", frame="00003"
    """
    # Rimuove il suffisso _inference_101_skip
    base_name = folder_name.replace("_inference_101_skip", "")

    # Controlla se contiene skip (s2, s3) - da ignorare
    if "_s2" in base_name or "_s3" in base_name:
        return None, None  # Ignora questi frame

    # Pattern per formato: nomeVideo_numeroVideo_numeroFrame
    # Es: bike-packing_1_00004
    parts = base_name.split("_")

    if len(parts) >= 3:
        # Caso: bike-packing_1_00004
        video_base = "_".join(parts[:-1])  # bike-packing_1
        frame = parts[-1]                  # 00004
        return video_base, frame
    elif len(parts) == 2:
        # Caso: 1_00003 (video senza nome descrittivo)
        video_base = parts[0]              # 1
        frame = parts[1]                   # 00003
        return video_base, frame
    else:
        # Fallback
        return base_name, "00000"


def get_video_base_name(video_base: str):
    """
    Restituisce il nome base del video per raggruppare i frame.
    """
    return video_base


def reorganize_split(input_split_dir: Path, output_split_dir: Path, split_name: str):
    """
    Riorganizza un singolo split raggruppando i frame per video.
    """
    if not input_split_dir.exists():
        print(f"⚠️  Split '{split_name}' non trovato in {input_split_dir}")
        return

    # Trova tutte le cartelle di frame
    frame_folders = [d for d in input_split_dir.iterdir() if d.is_dir() and d.name.endswith("_inference_101_skip")]

    if not frame_folders:
        print(f"⚠️  Nessuna cartella di inferenza trovata in {input_split_dir}")
        return

    print(f"📁 Split '{split_name}': trovate {len(frame_folders)} cartelle di frame")

    # Raggruppa le cartelle per video
    video_groups = defaultdict(list)
    skipped_count = 0

    for folder in frame_folders:
        video_base, frame = extract_video_info(folder.name)

        # Ignora i frame con skip (s2, s3)
        if video_base is None:
            skipped_count += 1
            continue

        video_groups[video_base].append((folder, frame))

    print(f"📊 Raggruppati in {len(video_groups)} video (ignorati {skipped_count} frame con skip)")

    # Crea le cartelle di output per ogni video
    ensure(output_split_dir)

    for video_base, frame_list in tqdm(video_groups.items(), desc=f"[{split_name}] Riorganizzazione"):
        # Crea la cartella del video
        video_output_dir = output_split_dir / f"{video_base}_inference_101_skip"
        ensure(video_output_dir)

        # Copia tutti i frame del video nella cartella
        for frame_folder, frame_num in frame_list:
            # Trova il file PNG nella cartella del frame
            png_files = list(frame_folder.glob("*.png"))

            if png_files:
                source_file = png_files[0]  # Dovrebbe esserci un solo PNG

                # Determina il nome del file di destinazione
                # Usa il nome originale del file se disponibile, altrimenti usa il frame number
                if source_file.stem != frame_num:
                    dest_filename = source_file.name
                else:
                    dest_filename = f"{frame_num}.png"

                dest_file = video_output_dir / dest_filename

                # Copia il file
                shutil.copy2(source_file, dest_file)
            else:
                print(f"⚠️  Nessun file PNG trovato in {frame_folder}")

    print(f"✅ Split '{split_name}' riorganizzato: {len(video_groups)} video con {len(frame_folders) - skipped_count} frame validi")


def main():
    args = cli()
    
    input_root = Path(args.input).resolve()
    output_root = Path(args.output).resolve()
    
    print(f"🔄 Riorganizzazione inferenze ResNet-101 skip")
    print(f"📂 Input:  {input_root}")
    print(f"📁 Output: {output_root}")
    
    if not input_root.exists():
        print(f"❌ Cartella di input non trovata: {input_root}")
        return
    
    # Crea la cartella di output
    ensure(output_root)
    
    # Processa tutti gli split
    for split in args.splits:
        input_split_dir = input_root / split
        output_split_dir = output_root / split
        
        reorganize_split(input_split_dir, output_split_dir, split)
    
    print(f"\n🎉 Riorganizzazione completata!")
    print(f"📁 Nuova struttura salvata in: {output_root}")
    print(f"\n📋 Struttura creata:")
    print(f"   {output_root}/")
    for split in args.splits:
        if (output_root / split).exists():
            video_dirs = [d for d in (output_root / split).iterdir() if d.is_dir()]
            print(f"   ├── {split}/ ({len(video_dirs)} video)")
            for i, video_dir in enumerate(sorted(video_dirs)[:3]):  # Mostra solo i primi 3
                frame_count = len(list(video_dir.glob("*.png")))
                prefix = "│   ├──" if i < 2 else "│   └──"
                print(f"   {prefix} {video_dir.name}/ ({frame_count} frame)")
            if len(video_dirs) > 3:
                print(f"   │   └── ... e altri {len(video_dirs) - 3} video")


if __name__ == "__main__":
    main()
